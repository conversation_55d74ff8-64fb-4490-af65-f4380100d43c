import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate } from '@tanstack/react-router';
import React, { useRef, useState, useEffect } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { useAuth } from '@/hooks/useAuth';
import purchaseRequestFormSchemaJson from '@/formSchemas/purchaseRequestForm.json';
import { useCategories } from '@/hooks/useCatalog';
import { useCreatePurchaseRequest } from '@/hooks/usePurchaseRequest';
import { ROUTES } from '@/constants/routes.constant';
import { 
  CreatePurchaseRequestRequest, 
  ProductSelectionItem, 
  PurchaseRequestItem,
  RequestType,
  ProcurementSource
} from '@/types/catalog.types';
import './CreatePurchaseRequest.css';

const CreatePurchaseRequest: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const { user } = useAuth();
  const { organizationId } = useOrganizationContext();

  // State for form schema
  const [purchaseRequestFormSchema, setPurchaseRequestFormSchema] = useState<FormSchema>(
    purchaseRequestFormSchemaJson as FormSchema
  );

  // Fetch categories for the dropdown
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Create purchase request mutation
  const createPurchaseRequestMutation = useCreatePurchaseRequest();

  // Update form schema with category options
  useEffect(() => {
    const updatedSchema = { ...purchaseRequestFormSchema };

    // Update category options
    const categoryField = updatedSchema.fields.find(field => field.name === 'productCategoryId');
    if (categoryField && categoryField.type === 'select') {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: category.id?.toString() || ''
      }));
    }

    setPurchaseRequestFormSchema(updatedSchema);
  }, [categories]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      if (!user?.id || !organizationId) {
        toast.current?.showError('User or organization information is missing');
        return;
      }

      // Validate product selection
      const productSelection: ProductSelectionItem[] = data.productSelection || [];
      if (productSelection.length === 0) {
        toast.current?.showError('Please select at least one product');
        return;
      }

      // Convert product selection to purchase request items
      const purchaseRequestItems: PurchaseRequestItem[] = productSelection.map(item => ({
        productId: item.type === 'product' ? item.productId : undefined,
        organizationProductId: item.type === 'organizationProduct' ? item.organizationProductId : undefined,
        quantity: item.quantity,
        onlineUrl: item.onlineUrl
      }));

      // Convert expected delivery date to ISO string
      const expectedDeliveryDate = data.expectedDeliveryDate instanceof Date 
        ? data.expectedDeliveryDate.toISOString()
        : data.expectedDeliveryDate;

      // Create the purchase request payload
      const purchaseRequestPayload: CreatePurchaseRequestRequest = {
        requestedById: user.id,
        organizationId: organizationId,
        requestType: data.requestType as RequestType,
        procurementSource: data.procurementSource as ProcurementSource,
        description: data.description || '',
        expectedDeliveryDate: expectedDeliveryDate,
        productCategoryId: parseInt(data.productCategoryId),
        purchaseRequestItems: purchaseRequestItems
      };

      // Submit the purchase request
      await createPurchaseRequestMutation.mutateAsync({
        organizationId: organizationId,
        purchaseRequest: purchaseRequestPayload
      });

      // Show success message
      toast.current?.showSuccess('Purchase request created successfully');

      // Navigate back to dashboard after a short delay
      setTimeout(() => {
        navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DASHBOARD });
      }, 1500);

    } catch (error: any) {
      console.error('Error creating purchase request:', error);
      const errorMessage = error?.response?.data?.message || 
                          error?.message || 
                          'Failed to create purchase request';
      toast.current?.showError(errorMessage);
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DASHBOARD });
  };

  // Get default values for the form
  const getDefaultValues = () => {
    return {
      productCategoryId: '',
      productSelection: [],
      expectedDeliveryDate: null,
      procurementSource: '',
      requestType: '',
      description: ''
    };
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    // Handle any specific field change logic here if needed
    console.log(`Field ${field} changed to:`, value);
  };

  return (
    <div className="create-purchase-request p-4">
      <Toast ref={toast} position="top-right" />
      <Card
        title="Create Purchase Request"
        subtitle="Submit a new purchase request for approval"
        variant="elevated"
        padding="large"
        className="max-w-4xl mx-auto"
      >
        <DynamicForm
          schema={purchaseRequestFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
          isSubmitButtonLoading={createPurchaseRequestMutation.isPending}
        />
      </Card>
    </div>
  );
};

export default CreatePurchaseRequest;
