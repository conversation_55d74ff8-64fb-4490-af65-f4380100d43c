/* Create Purchase Request Page Styles */
.create-purchase-request {
  min-height: 100vh;
}

.create-purchase-request .p-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.create-purchase-request .p-card-title {
  color: var(--primary-color);
  font-weight: 600;
}

.create-purchase-request .p-card-subtitle {
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
}

/* Product Selection Styles */
.product-selection {
  width: 100%;
}

.product-selection .p-autocomplete {
  width: 100%;
}

.product-selection .selected-products {
  margin-top: 1rem;
}

.product-selection .selected-products .p-card {
  border: 1px solid var(--surface-border);
  background: var(--surface-ground);
}

.product-selection .selected-products .p-card:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
}

.product-selection .p-inputnumber {
  width: 4rem;
}

.product-selection .p-inputnumber .p-inputnumber-input {
  text-align: center;
}

/* Empty state styles */
.product-selection .border-dashed {
  border-style: dashed !important;
  border-width: 2px !important;
  border-color: var(--surface-border) !important;
}

.product-selection .border-round {
  border-radius: var(--border-radius);
}

/* Form field spacing */
.create-purchase-request .form-field-wrapper {
  margin-bottom: 1.5rem;
}

.create-purchase-request .form-field-wrapper:last-child {
  margin-bottom: 0;
}

/* Button styles */
.create-purchase-request .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-border);
}

.create-purchase-request .form-actions .p-button {
  min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
  .create-purchase-request {
    padding: 1rem;
  }
  
  .create-purchase-request .max-w-4xl {
    max-width: 100%;
  }
  
  .create-purchase-request .form-actions {
    flex-direction: column;
  }
  
  .create-purchase-request .form-actions .p-button {
    width: 100%;
  }
  
  .product-selection .selected-products .p-card .flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }
  
  .product-selection .selected-products .p-card .flex .flex:last-child {
    flex-direction: row;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }
}

/* Loading state */
.create-purchase-request .p-button.p-button-loading {
  pointer-events: none;
}

/* Error state */
.create-purchase-request .p-invalid {
  border-color: var(--red-500) !important;
}

.create-purchase-request .p-error {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
