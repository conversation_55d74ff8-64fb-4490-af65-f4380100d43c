import { loginRoute } from "@/routes/public/login.route";
import { indexRoute } from "@/routes/public/index.route";
import { rootRoute } from "@/routes/root.route";
import { appRoute } from "@/routes/private/app.route";
import { dashboardRoute } from "@/routes/private/dashboard.route";
import { organizationDetailsRoute } from "@/routes/private/organizationDetails.route";
import { notFoundRoute } from "@/routes/common/notFound.route";
import { addUserRoute } from "./private/addUser.route";
import { userDetailsRoute } from "./private/userDetails.route";
import { editUserRoute } from "./private/editUser.route";
import { locationDetailsRoute } from "./private/locationDetails.route";
import { departmentDetailsRoute } from "./private/departmentDetails.route";
import { designationDetailsRoute } from "./private/designationDetails.route";
import { organizationItemCatalogRoute } from "./private/organizationItemCatalog.route";
import { addProductRoute } from "./private/addProduct.route";
import { editProductRoute } from "./private/editProduct.route";
import { addCategoryRoute } from "./private/addCategory.route";
import { editCategoryRoute } from "./private/editCategory.route";
import { addSubcategoryRoute } from "./private/addSubcategory.route";
import { editSubcategoryRoute } from "./private/editSubcategory.route";
import { approvalWorkflowsRoute } from "./private/approvalWorkflows.route";
import { addApprovalWorkflowRoute } from "./private/addApprovalWorkflow.route";
import { editApprovalWorkflowRoute } from "./private/editApprovalWorkflow.route";
import { organizationListRoute } from "./private/organizationList.route";
import { addOrganizationRoute } from "./private/addOrganization.route";
import { editOrganizationRoute } from "./private/editOrganization.route";
import { productCatalogRoute } from "./private/productCatalog.route";
import { addProductCatalogRoute } from "./private/addProductCatalog.route";
import { editProductCatalogRoute } from "./private/editProductCatalog.route";

export const routeTree = rootRoute.addChildren([
    indexRoute,
    loginRoute,
    appRoute.addChildren([
        dashboardRoute,
        organizationDetailsRoute,
        userDetailsRoute,
        locationDetailsRoute,
        departmentDetailsRoute,
        designationDetailsRoute,
        organizationItemCatalogRoute,
        addUserRoute,
        editUserRoute,
        addProductRoute,
        editProductRoute,
        addCategoryRoute,
        editCategoryRoute,
        addSubcategoryRoute,
        editSubcategoryRoute,
        approvalWorkflowsRoute,
        addApprovalWorkflowRoute,
        editApprovalWorkflowRoute,
        organizationListRoute,
        addOrganizationRoute,
        editOrganizationRoute,
        productCatalogRoute,
        addProductCatalogRoute,
        editProductCatalogRoute
    ]),
    notFoundRoute,
]);