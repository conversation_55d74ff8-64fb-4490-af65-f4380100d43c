/**
 * Purchase Request API Service
 * Handles all purchase request related API operations
 */

import { apiClient } from './client';
import { 
  PurchaseRequest, 
  CreatePurchaseRequestRequest,
  Product,
  OrganizationProduct
} from '@/types/catalog.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

/**
 * Create a new purchase request
 */
export async function createPurchaseRequest(
  organizationId: number,
  purchaseRequest: CreatePurchaseRequestRequest
): Promise<ApiResponse<PurchaseRequest>> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    const response = await apiClient.post<PurchaseRequest>(PURCHASE_REQUESTS_URL, purchaseRequest);
    return {
      data: response,
      message: 'Purchase request created successfully',
      status: 200
    };
  } catch (error) {
    console.error('Error creating purchase request:', error);
    throw error;
  }
}

/**
 * Get purchase request by ID
 */
export async function getPurchaseRequestById(
  organizationId: number,
  id: number
): Promise<PurchaseRequest> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    const response = await apiClient.get<PurchaseRequest>(`${PURCHASE_REQUESTS_URL}/${id}`);
    return response;
  } catch (error) {
    console.error(`Error fetching purchase request with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Get all purchase requests for an organization
 */
export async function getPurchaseRequests(
  organizationId: number,
  params?: QueryParams
): Promise<PaginatedResponse<PurchaseRequest>> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    const response = await apiClient.get<PurchaseRequest[]>(PURCHASE_REQUESTS_URL);
    let purchaseRequests = response || [];

    // Apply client-side filtering if search query is provided
    if (params?.search) {
      const searchLower = params.search.toLowerCase();
      purchaseRequests = purchaseRequests.filter(pr => 
        pr.description?.toLowerCase().includes(searchLower) ||
        pr.requestType.toLowerCase().includes(searchLower) ||
        pr.procurementSource.toLowerCase().includes(searchLower)
      );
    }

    // Apply client-side sorting
    if (params?.sortBy) {
      purchaseRequests.sort((a, b) => {
        const aValue = a[params.sortBy as keyof PurchaseRequest];
        const bValue = b[params.sortBy as keyof PurchaseRequest];
        
        if (aValue === undefined || bValue === undefined) return 0;
        
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return params.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Apply client-side pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = purchaseRequests.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: purchaseRequests.length,
      page,
      limit,
      totalPages: Math.ceil(purchaseRequests.length / limit)
    };
  } catch (error) {
    console.error('Error fetching purchase requests:', error);
    throw error;
  }
}

/**
 * Get purchase requests by user ID
 */
export async function getPurchaseRequestsByUserId(
  organizationId: number,
  userId: number
): Promise<PurchaseRequest[]> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    const response = await apiClient.get<PurchaseRequest[]>(`${PURCHASE_REQUESTS_URL}/requested-by/${userId}`);
    return response;
  } catch (error) {
    console.error(`Error fetching purchase requests for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Delete a purchase request
 */
export async function deletePurchaseRequest(
  organizationId: number,
  id: number
): Promise<ApiResponse<void>> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    await apiClient.delete<void>(`${PURCHASE_REQUESTS_URL}/${id}`);
    return {
      data: undefined,
      message: 'Purchase request deleted successfully',
      status: 204
    };
  } catch (error) {
    console.error(`Error deleting purchase request with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Search products by category and query
 */
export async function searchProductsByCategory(
  categoryId: number,
  query: string,
  organizationId?: number
): Promise<Product[]> {
  const PRODUCTS_URL = `/v1/products`;
  try {
    // First get products by category
    const categoryProducts = await apiClient.get<Product[]>(`${PRODUCTS_URL}/category/${categoryId}${organizationId ? `?organizationId=${organizationId}` : ''}`);
    
    // Filter by search query if provided
    if (query && query.length >= 2) {
      const searchLower = query.toLowerCase();
      return categoryProducts.filter(product => 
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }
    
    return categoryProducts;
  } catch (error) {
    console.error('Error searching products by category:', error);
    throw error;
  }
}

/**
 * Search organization products by category and query
 */
export async function searchOrganizationProductsByCategory(
  organizationId: number,
  categoryId: number,
  query: string
): Promise<OrganizationProduct[]> {
  const ORG_PRODUCTS_URL = `/v1/products/org-products`;
  try {
    // First get organization products by category
    const categoryProducts = await apiClient.get<OrganizationProduct[]>(`${ORG_PRODUCTS_URL}/category/${categoryId}?organizationId=${organizationId}`);
    
    // Filter by search query if provided
    if (query && query.length >= 2) {
      const searchLower = query.toLowerCase();
      return categoryProducts.filter(product => 
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }
    
    return categoryProducts;
  } catch (error) {
    console.error('Error searching organization products by category:', error);
    throw error;
  }
}

/**
 * Approve a purchase request
 */
export async function approvePurchaseRequest(
  organizationId: number,
  id: number
): Promise<ApiResponse<void>> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    await apiClient.post<void>(`${PURCHASE_REQUESTS_URL}/${id}/approve`);
    return {
      data: undefined,
      message: 'Purchase request approved successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error approving purchase request with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Reject a purchase request
 */
export async function rejectPurchaseRequest(
  organizationId: number,
  id: number
): Promise<ApiResponse<void>> {
  const PURCHASE_REQUESTS_URL = `/v1/organization/${organizationId}/purchaserequests`;
  try {
    await apiClient.post<void>(`${PURCHASE_REQUESTS_URL}/${id}/reject`);
    return {
      data: undefined,
      message: 'Purchase request rejected successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error rejecting purchase request with ID ${id}:`, error);
    throw error;
  }
}
