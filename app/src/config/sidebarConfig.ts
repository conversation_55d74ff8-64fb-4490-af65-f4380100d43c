import { ROUTES } from '@/constants/routes.constant';
import { MenuItem } from 'primereact/menuitem';
import { Role } from '@/types/api/user';

export interface SidebarConfigItem extends MenuItem {
  to?: string;
  items?: SidebarConfigItem[];
  isComingSoon?: boolean;
  roles?: Role[];
}

export const sidebarConfig: SidebarConfigItem[] = [
  {
    label: 'Dashboard',
    icon: 'pi pi-home',
    to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DASHBOARD,
    isComingSoon: false,
    roles: [Role.ROLE_USER, Role.ROLE_ORG_ADMIN, Role.ROLE_SUPER_ADMIN]
  },
  {
    label: 'Purchase Request',
    icon: 'pi pi-cart-plus',
    to: '/users',
    isComingSoon: true,
    roles: [Role.ROLE_USER, Role.ROLE_ORG_ADMIN]
  },
  {
    label: 'Product Catalogue',
    icon: 'pi pi-tags',
    to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.PRODUCT_CATALOG,
    roles: [Role.ROLE_SUPER_ADMIN, Role.ROLE_ORG_ADMIN]
  },
  {
    label: 'Vendors',
    icon: 'pi pi-truck',
    to: "/user",
    isComingSoon: true,
    roles: [Role.ROLE_SUPER_ADMIN]
  },
  {
    label: 'Organizations',
    icon: 'pi pi-building',
    to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_LIST,
    roles: [Role.ROLE_SUPER_ADMIN]
  },
  {
    label: 'Organization Settings',
    icon: 'pi pi-cog',
    isComingSoon: false,
    roles: [Role.ROLE_ORG_ADMIN],
    items: [
      {
        label: 'Approval Workflows',
        icon: 'pi pi-check-circle',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.APPROVAL_WORKFLOWS,
        isComingSoon: false
      },
      {
        label: 'Organization Catalogue',
        icon: 'pi pi-list',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_ITEM_CATALOG,
        isComingSoon: false,
        roles: [Role.ROLE_ORG_ADMIN]
      },
      {
        label: 'Employees',
        icon: 'pi pi-users',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS,
        isComingSoon: false,
        roles: [Role.ROLE_ORG_ADMIN, Role.ROLE_SUPER_ADMIN]
      },
      {
        label: 'Designations',
        icon: 'pi pi-id-card',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DESIGNATION_DETAILS,
        isComingSoon: false
      },
      {
        label: 'Departments',
        icon: 'pi pi-sitemap',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.DEPARTMENT_DETAILS,
        isComingSoon: false
      },
      {
        label: 'Locations',
        icon: 'pi pi-map-marker',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.LOCATION_DETAILS,
        isComingSoon: false
      },
      {
        label: 'Organization Details',
        icon: 'pi pi-image',
        to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_DETAILS,
        isComingSoon: false,
        roles: [Role.ROLE_ORG_ADMIN]
      },

    ]
  }
];