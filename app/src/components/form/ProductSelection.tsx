import { useState, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { AutoComplete } from 'primereact/autocomplete';
import { InputNumber } from 'primereact/inputnumber';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { useOrganizationContext } from '@/context/OrganizationContext';
import {
  useSearchProductsByCategory,
  useSearchOrganizationProductsByCategory
} from '@/hooks/usePurchaseRequest';
import {
  ProductSelectionItem
} from '@/types/catalog.types';

interface ProductSelectionProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  categoryId?: number;
}

interface ProductOption {
  label: string;
  value: string;
  type: 'product' | 'organizationProduct';
  productId?: number;
  organizationProductId?: number;
  productName: string;
}

export const ProductSelection = ({
  name,
  label,
  placeholder,
  icon,
  categoryId
}: ProductSelectionProps) => {
  const {
    control,
    formState: { errors },
    watch
  } = useFormContext();

  const { organizationId } = useOrganizationContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<ProductOption[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<ProductSelectionItem[]>([]);

  // Watch for category changes
  const watchedCategoryId = watch('productCategoryId');
  const effectiveCategoryId = categoryId || watchedCategoryId;

  const error = errors[name]?.message as string | undefined;

  // Search for products and organization products
  const { data: products = [] } = useSearchProductsByCategory(
    effectiveCategoryId, 
    searchQuery, 
    organizationId
  );
  
  const { data: orgProducts = [] } = useSearchOrganizationProductsByCategory(
    organizationId || 0, 
    effectiveCategoryId, 
    searchQuery
  );

  // Update suggestions when search results change
  useEffect(() => {
    const productOptions: ProductOption[] = products.map(product => ({
      label: `${product.name} (Global Product)`,
      value: `product_${product.id}`,
      type: 'product' as const,
      productId: product.id,
      productName: product.name
    }));

    const orgProductOptions: ProductOption[] = orgProducts.map(product => ({
      label: `${product.name} (Organization Product)`,
      value: `org_${product.id}`,
      type: 'organizationProduct' as const,
      organizationProductId: product.id,
      productName: product.name
    }));

    setSuggestions([...productOptions, ...orgProductOptions]);
  }, [products, orgProducts]);

  // Handle search input
  const handleSearch = (event: { query: string }) => {
    setSearchQuery(event.query);
  };

  // Add a new product selection
  const addProduct = (selectedOption: ProductOption) => {
    const newProduct: ProductSelectionItem = {
      id: `${Date.now()}_${Math.random()}`,
      type: selectedOption.type,
      productId: selectedOption.productId,
      organizationProductId: selectedOption.organizationProductId,
      productName: selectedOption.productName,
      quantity: 1
    };

    const updatedProducts = [...selectedProducts, newProduct];
    setSelectedProducts(updatedProducts);
    setSearchQuery('');
  };

  // Remove a product selection
  const removeProduct = (productId: string) => {
    const updatedProducts = selectedProducts.filter(p => p.id !== productId);
    setSelectedProducts(updatedProducts);
  };

  // Update product quantity
  const updateQuantity = (productId: string, quantity: number) => {
    const updatedProducts = selectedProducts.map(p => 
      p.id === productId ? { ...p, quantity: quantity || 1 } : p
    );
    setSelectedProducts(updatedProducts);
  };

  // Custom item template for dropdown
  const itemTemplate = (item: ProductOption) => {
    return (
      <div className="flex align-items-center">
        <i className={`pi ${item.type === 'product' ? 'pi-box' : 'pi-building'} mr-2`}></i>
        <div>
          <div className="font-medium">{item.productName}</div>
          <div className="text-sm text-color-secondary">
            {item.type === 'product' ? 'Global Product' : 'Organization Product'}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          // Update form value when selectedProducts changes
          useEffect(() => {
            field.onChange(selectedProducts);
          }, [selectedProducts, field]);

          return (
            <div className={`product-selection ${error ? 'p-invalid' : ''}`}>
              {/* Product Search */}
              <div className="mb-3">
                <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
                  {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
                  <AutoComplete
                    value={searchQuery}
                    suggestions={suggestions}
                    completeMethod={handleSearch}
                    onChange={(e) => setSearchQuery(typeof e.value === 'string' ? e.value : '')}
                    onSelect={(e) => {
                      if (e.value && typeof e.value === 'object') {
                        addProduct(e.value);
                      }
                    }}
                    placeholder={placeholder || 'Search for products...'}
                    itemTemplate={itemTemplate}
                    className="w-full"
                    disabled={!effectiveCategoryId}
                    emptyMessage={!effectiveCategoryId ? 'Please select a category first' : 'No products found'}
                  />
                </div>
                {!effectiveCategoryId && (
                  <small className="text-color-secondary">
                    Please select a product category first to search for products.
                  </small>
                )}
              </div>

              {/* Selected Products */}
              {selectedProducts.length > 0 && (
                <div className="selected-products">
                  <h6 className="mb-2">Selected Products:</h6>
                  {selectedProducts.map((product) => (
                    <Card key={product.id} className="mb-2 p-2">
                      <div className="flex align-items-center justify-content-between">
                        <div className="flex align-items-center flex-1">
                          <i className={`pi ${product.type === 'product' ? 'pi-box' : 'pi-building'} mr-2`}></i>
                          <div className="flex-1">
                            <div className="font-medium">{product.productName}</div>
                            <div className="text-sm text-color-secondary">
                              {product.type === 'product' ? 'Global Product' : 'Organization Product'}
                            </div>
                          </div>
                        </div>
                        <div className="flex align-items-center gap-2">
                          <label className="text-sm">Qty:</label>
                          <InputNumber
                            value={product.quantity}
                            onValueChange={(e) => updateQuantity(product.id, e.value || 1)}
                            min={1}
                            max={9999}
                            className="w-4rem"
                            showButtons
                            buttonLayout="horizontal"
                          />
                          <Button
                            icon="pi pi-trash"
                            className="p-button-danger p-button-text p-button-sm"
                            onClick={() => removeProduct(product.id)}
                            tooltip="Remove product"
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {selectedProducts.length === 0 && (
                <div className="text-center p-3 border-1 border-dashed surface-border border-round">
                  <i className="pi pi-shopping-cart text-4xl text-color-secondary mb-2"></i>
                  <p className="text-color-secondary m-0">No products selected</p>
                  <small className="text-color-secondary">
                    Search and select products from the dropdown above
                  </small>
                </div>
              )}
            </div>
          );
        }}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
